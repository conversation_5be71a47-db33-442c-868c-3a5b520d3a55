<?php
/**
 * Admin Functions for Coolify Module
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

require_once __DIR__ . '/CoolifyAPI.php';
require_once __DIR__ . '/../templates/AdminTemplates.php';

/**
 * View Service Details - Enhanced with new template
 */
function coolify_viewService($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured</div>';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return '<div class="alert alert-warning">Service UUID not found</div>';
        }

        $serviceInfo = $api->getService($serviceUuid);
        $serverResources = null;

        // Try to get server resources if server UUID is available
        if (!empty($serverConfig['server_uuid'])) {
            try {
                $serverResources = $api->getServerResources($serverConfig['server_uuid']);
            } catch (Exception $e) {
                // Server resources are optional, don't fail if unavailable
                logModuleCall('coolify', 'getServerResources', $params, $e->getMessage(), '');
            }
        }

        return coolify_renderAdminServiceOverview($params, $serviceInfo, $serverResources);

    } catch (Exception $e) {
        logModuleCall('coolify', 'viewService', $params, $e->getMessage(), $e->getTraceAsString());
        return '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Restart Service
 */
function coolify_restartService($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'API token not configured';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);
        
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->restartService($serviceUuid);
        
        logModuleCall('coolify', 'restartService', $params, $result, '', array($apiToken));
        
        if ($result) {
            return '<div class="alert alert-success">n8n service restart initiated successfully. It may take a few moments to complete.</div>';
        } else {
            return '<div class="alert alert-warning">n8n restart request sent, but no confirmation received.</div>';
        }
        
    } catch (Exception $e) {
        logModuleCall('coolify', 'restartService', $params, $e->getMessage(), $e->getTraceAsString());
        return '<div class="alert alert-danger">Error restarting service: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * View Service Logs - Enhanced with new template
 */
function coolify_viewLogs($params)
{
    try {
        // Get API token from server access hash (primary) or server password field
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['serverpassword'];

        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured</div>';
        }

        // Get server configuration
        $serverConfig = coolify_getServerConfiguration($params['serverid']);
        $coolifyUrl = !empty($serverConfig['coolify_url']) ? $serverConfig['coolify_url'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);

        if (!$serviceUuid) {
            return '<div class="alert alert-warning">Service UUID not found</div>';
        }

        $logs = $api->getServiceLogs($serviceUuid, 200);
        $serviceInfo = null;

        // Try to get service info for enhanced display
        try {
            $serviceInfo = $api->getService($serviceUuid);
        } catch (Exception $e) {
            // Service info is optional for logs display
        }

        logModuleCall('coolify', 'viewLogs', $params, 'Logs retrieved successfully', '');

        return coolify_renderEnhancedLogs($logs, $serviceInfo);

    } catch (Exception $e) {
        logModuleCall('coolify', 'viewLogs', $params, $e->getMessage(), $e->getTraceAsString());
        return '<div class="alert alert-danger">Error retrieving logs: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Update Service Configuration
 */
function coolify_updateConfig($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'API token not configured';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);
        
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        // Get current service info
        $serviceInfo = $api->getService($serviceUuid);
        
        $output = '<div class="panel panel-default">';
        $output .= '<div class="panel-heading"><h4>Update n8n Service Configuration</h4></div>';
        $output .= '<div class="panel-body">';
        
        $output .= '<form method="post" action="">';
        $output .= '<input type="hidden" name="action" value="updateServiceConfig">';
        $output .= '<input type="hidden" name="serviceid" value="' . $params['serviceid'] . '">';
        
        $output .= '<div class="form-group">';
        $output .= '<label>Memory Limit (MB):</label>';
        $currentMemory = isset($serviceInfo['memory_limit']) ? $serviceInfo['memory_limit'] : $params['configoption8'];
        $output .= '<input type="number" name="memory_limit" class="form-control" value="' . htmlspecialchars($currentMemory) . '" min="128" max="4096">';
        $output .= '</div>';
        
        $output .= '<div class="form-group">';
        $output .= '<label>Environment Variables:</label>';
        $output .= '<textarea name="env_vars" class="form-control" rows="8" placeholder="KEY1=value1&#10;KEY2=value2">';
        if (isset($serviceInfo['environment_variables']) && is_array($serviceInfo['environment_variables'])) {
            foreach ($serviceInfo['environment_variables'] as $key => $value) {
                $output .= htmlspecialchars($key . '=' . $value) . "\n";
            }
        }
        $output .= '</textarea>';
        $output .= '<small class="help-block">One environment variable per line in KEY=value format</small>';
        $output .= '</div>';
        
        $output .= '<button type="submit" class="btn btn-primary">Update Configuration</button>';
        $output .= '</form>';
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
        
    } catch (Exception $e) {
        return '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Handle service configuration updates
 */
function coolify_handleConfigUpdate($params)
{
    if ($_POST['action'] == 'updateServiceConfig' && $_POST['serviceid'] == $params['serviceid']) {
        try {
            // Use server access hash as primary API token, fallback to config option
            $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
            
            if (empty($apiToken)) {
                return 'API token not configured';
            }
            
            // Get Coolify instance URL from configuration
            $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
            $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
            
            $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
            $serviceUuid = getServiceUuid($params['serviceid']);
            
            if (!$serviceUuid) {
                return 'Service UUID not found';
            }
            
            $updateData = array();
            
            // Update memory limit if provided
            if (isset($_POST['memory_limit']) && is_numeric($_POST['memory_limit'])) {
                $updateData['memory_limit'] = intval($_POST['memory_limit']);
            }
            
            // Update environment variables if provided
            if (isset($_POST['env_vars']) && !empty($_POST['env_vars'])) {
                $envVars = array();
                $lines = explode("\n", $_POST['env_vars']);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line) && strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $envVars[trim($key)] = trim($value);
                    }
                }
                if (!empty($envVars)) {
                    $api->setServiceEnvironmentVariables($serviceUuid, $envVars);
                }
            }
            
            if (!empty($updateData)) {
                $result = $api->updateService($serviceUuid, $updateData);
            }
            
            return '<div class="alert alert-success">n8n service configuration updated successfully.</div>';
            
        } catch (Exception $e) {
            logModuleCall('coolify', 'updateConfig', $params, $e->getMessage(), $e->getTraceAsString());
            return '<div class="alert alert-danger">Error updating configuration: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
    
    return coolify_updateConfig($params);
} 