# 🚀 Coolify Scaling Setup Guide

## Overview

The Coolify n8n module has been redesigned to support **multiple Coolify instances** for better scalability. Instead of storing Coolify-specific settings in product configuration, they are now stored at the **server level**, allowing you to:

- ✅ **Multiple Coolify Instances**: Add as many Coolify servers as needed
- ✅ **Load Distribution**: Distribute services across different Coolify instances
- ✅ **Geographic Distribution**: Deploy services in different regions
- ✅ **Environment Separation**: Separate production, staging, and development
- ✅ **Easy Scaling**: Add new Coolify instances without creating new products

## 🔄 What Changed

### Before (Product-Level Configuration)
```
Product 1: n8n Basic (Coolify Instance A settings)
Product 2: n8n Pro (Coolify Instance A settings)
Product 3: n8n Enterprise (Coolify Instance B settings) ❌ Required separate product
```

### After (Server-Level Configuration)
```
Server 1: Coolify Instance A (US East)
Server 2: Coolify Instance B (EU West)
Server 3: Coolify Instance C (Asia Pacific)

Product 1: n8n Basic (can use any server)
Product 2: n8n Pro (can use any server)
Product 3: n8n Enterprise (can use any server)
```

## 📋 New Configuration Structure

### Server-Level Settings (Per Coolify Instance)
- **API Token**: Coolify Bearer token
- **Project UUID**: Target Coolify project
- **Environment Name**: Deployment environment
- **Server UUID**: Deployment server
- **Destination UUID**: Container destination
- **Coolify URL**: Coolify instance URL
- **Base Domain**: Domain for services

### Product-Level Settings (Per Service Plan)
- **Memory Limit**: Container memory allocation
- **Timezone**: Default timezone for instances
- **Docker Image**: n8n image version

## 🛠️ Setup Instructions

### Step 1: Configure Coolify Servers

1. **Go to WHMCS Admin Area**
   - Navigate to `Setup > Products/Services > Servers`
   - Click "Add New Server"

2. **Create First Coolify Server**
   ```
   Server Name: Coolify US East
   Hostname: coolify-us.yourdomain.com
   Status Address: http://************:8000
   Type: Other
   Active: Yes
   Access Hash: your-coolify-api-token

   Nameserver 1: your-project-uuid
   Nameserver 2: your-server-uuid
   Nameserver 3: your-destination-uuid
   Nameserver 4: production
   Nameserver 5: us.customers.yourdomain.com
   ```

3. **Create Additional Coolify Servers**
   ```
   Server Name: Coolify EU West
   Hostname: coolify-eu.yourdomain.com
   Status Address: http://*************:8000
   Access Hash: your-eu-coolify-api-token

   Nameserver 1: eu-project-uuid
   Nameserver 2: eu-server-uuid
   Nameserver 3: eu-destination-uuid
   Nameserver 4: production
   Nameserver 5: eu.customers.yourdomain.com
   ```

### Step 2: Configure Products

1. **Create/Edit n8n Products**
   - Go to `Setup > Products/Services > Products/Services`
   - Edit your n8n products

2. **Set Module Settings**
   ```
   Module Name: coolify
   Memory Limit: 512 (MB)
   Default Timezone: UTC
   Docker Image: docker.n8n.io/n8nio/n8n
   ```

3. **Assign Servers**
   - In the product configuration, assign the Coolify servers
   - You can assign multiple servers for load balancing

### Step 3: Test Configuration

1. **Create Test Service**
   - Order a test n8n service
   - Verify it provisions correctly

2. **Check Service Assignment**
   - Verify the service is created on the correct Coolify instance
   - Test access to the n8n dashboard

## 📊 Server Configuration Methods

### Method 1: Using Status Address + Nameserver Fields (Recommended)
```
Status Address: http://************:8000
Hostname: coolify.yourdomain.com
Nameserver 1: project-uuid-here
Nameserver 2: server-uuid-here
Nameserver 3: destination-uuid-here
Nameserver 4: production
Nameserver 5: customers.yourdomain.com
```

### Method 2: Simple URL Configuration
```
Status Address: http://************:8000
Hostname: coolify.yourdomain.com
```
*Note: You'll need to configure UUIDs in nameserver fields*

### Method 3: JSON in Server Name (Advanced)
```
Server Name: {"project_uuid":"uuid","server_uuid":"uuid","destination_uuid":"uuid"}
Status Address: http://************:8000
```

## 🌍 Multi-Region Setup Example

### US East Server
```
Server Name: Coolify US East
Status Address: http://************:8000
Hostname: coolify-us.yourdomain.com
Nameserver 1: us-project-uuid
Nameserver 2: us-server-uuid
Nameserver 3: us-destination-uuid
Nameserver 4: production
Nameserver 5: us.customers.yourdomain.com
```

### EU West Server
```
Server Name: Coolify EU West
Status Address: http://*************:8000
Hostname: coolify-eu.yourdomain.com
Nameserver 1: eu-project-uuid
Nameserver 2: eu-server-uuid
Nameserver 3: eu-destination-uuid
Nameserver 4: production
Nameserver 5: eu.customers.yourdomain.com
```

### Asia Pacific Server
```
Server Name: Coolify Asia Pacific
Status Address: http://************:8000
Hostname: coolify-ap.yourdomain.com
Nameserver 1: ap-project-uuid
Nameserver 2: ap-server-uuid
Nameserver 3: ap-destination-uuid
Nameserver 4: production
Nameserver 5: ap.customers.yourdomain.com
```

## ⚖️ Load Balancing Strategies

### 1. Geographic Distribution
- Assign customers to nearest Coolify instance
- Use WHMCS server groups for regional assignment

### 2. Capacity-Based Distribution
- Monitor server resources
- Assign new services to least loaded servers

### 3. Environment Separation
```
Server 1: Production Coolify
Server 2: Staging Coolify
Server 3: Development Coolify
```

### 4. Customer Tier Distribution
```
Basic Plan → Shared Coolify Instance
Pro Plan → Dedicated Coolify Instance
Enterprise → Private Coolify Instance
```

## 🔧 Advanced Configuration

### Custom Server Selection Logic

Create a hook to automatically select servers:

```php
// includes/hooks/coolify_server_selection.php
add_hook('ShoppingCartCheckoutCompletePage', 1, function($vars) {
    // Custom logic to assign Coolify server based on:
    // - Customer location
    // - Service plan
    // - Server capacity
    // - Geographic preferences
});
```

### Dynamic Server Assignment

```php
function selectOptimalCoolifyServer($customerId, $productId) {
    // Get customer location
    $customerLocation = getCustomerLocation($customerId);
    
    // Get available servers
    $servers = getCoolifyServers();
    
    // Select based on proximity and capacity
    return selectBestServer($servers, $customerLocation);
}
```

## 📈 Monitoring and Management

### Server Health Monitoring
- Monitor each Coolify instance separately
- Track resource usage per server
- Set up alerts for server issues

### Service Distribution Tracking
- Monitor service distribution across servers
- Track performance metrics per server
- Optimize server assignments

### Capacity Planning
- Monitor server capacity
- Plan for additional servers
- Scale based on demand

## 🔍 Troubleshooting

### Common Issues

**Server Configuration Not Found**
```
Error: Server configuration not found
Solution: Check server hostname field contains valid JSON
```

**Invalid JSON Configuration**
```
Error: Cannot parse server configuration
Solution: Validate JSON format in hostname field
```

**API Token Issues**
```
Error: API token not configured
Solution: Set token in server Access Hash field
```

### Debug Steps

1. **Check Server Configuration**
   ```php
   $config = coolify_getServerConfiguration($serverId);
   var_dump($config);
   ```

2. **Validate JSON**
   ```bash
   echo '{"project_uuid":"..."}' | jq .
   ```

3. **Test API Connection**
   - Use Coolify API directly
   - Verify token permissions
   - Check network connectivity

## 🚀 Migration from Old Configuration

### Step 1: Backup Current Settings
- Export current product configurations
- Document all Coolify settings

### Step 2: Create Server Configurations
- Create servers with old product settings
- Test each server configuration

### Step 3: Update Products
- Remove old Coolify settings from products
- Assign new servers to products

### Step 4: Test Migration
- Create test services
- Verify functionality
- Update documentation

## 📚 Best Practices

### Security
- ✅ Use separate API tokens per server
- ✅ Limit token permissions
- ✅ Rotate tokens regularly
- ✅ Monitor API usage

### Performance
- ✅ Distribute load across servers
- ✅ Monitor server resources
- ✅ Use geographic distribution
- ✅ Plan for peak usage

### Maintenance
- ✅ Document server configurations
- ✅ Test backup procedures
- ✅ Monitor service health
- ✅ Plan for updates

## 🎯 Benefits of New Architecture

### For Hosting Providers
- **Better Scalability**: Easy to add new Coolify instances
- **Geographic Distribution**: Serve customers globally
- **Resource Optimization**: Better load distribution
- **Easier Management**: Centralized server configuration

### For Customers
- **Better Performance**: Services deployed closer to them
- **Higher Availability**: Multiple server options
- **Consistent Experience**: Same products across all servers
- **Faster Provisioning**: Optimized server selection

The new server-based configuration provides a much more scalable and flexible architecture for managing multiple Coolify instances! 🎉
