# 📧 Welcome Email Setup Guide

## Overview

The Coolify n8n module includes a comprehensive welcome email system that automatically sends professional welcome emails to customers when their n8n service is provisioned.

## Features

### 🎨 Professional Email Design
- **Responsive HTML Template**: Works on desktop and mobile devices
- **Modern Styling**: Gradient header, clean layout, professional appearance
- **Brand Colors**: Customizable color scheme
- **Mobile Optimized**: Responsive design for all screen sizes

### 📋 Comprehensive Content
- **Service Details**: Access URL, database info, SSL certificate status
- **Quick Start Guide**: 5-step workflow creation process
- **Feature Overview**: Email automation, data sync, social media, e-commerce
- **Integration List**: 500+ available service integrations
- **Resource Links**: Documentation, templates, community, tutorials
- **Support Information**: Help resources and contact details

### 🔧 Technical Features
- **Automatic Sending**: Triggered when service is successfully provisioned
- **WHMCS Integration**: Uses WHMCS email system when available
- **Fallback Support**: Direct PHP mail as backup
- **Error Handling**: Graceful error handling with logging
- **Template Variables**: Dynamic content based on service details

## Setup Methods

### Method 1: Automatic Email (Recommended)

The module automatically sends welcome emails when services are provisioned. No additional setup required.

**How it works:**
1. Customer orders n8n service
2. Service is provisioned successfully
3. Welcome email is automatically sent
4. Email includes service details and getting started guide

### Method 2: WHMCS Email Template (Advanced)

For more control, you can create a custom WHMCS email template.

**Setup Steps:**

1. **Access WHMCS Admin Area**
   - Go to `Setup > Email Templates`
   - Click "Create New Email Template"

2. **Create Template**
   - **Template Name**: `n8n Welcome Email`
   - **Subject**: `🎉 Welcome to n8n! Your Workflow Automation Platform is Ready`
   - **Template Type**: `General`

3. **Import Template Content**
   - Copy content from `email_templates/n8n_welcome_email.tpl`
   - Paste into WHMCS email template editor
   - Save the template

4. **Configure Variables**
   Available template variables:
   ```
   {$client_name}           - Customer's full name
   {$service_domain}        - n8n service domain
   {$service_id}           - WHMCS service ID
   {$service_product_name} - Product name
   {$whmcs_url}            - WHMCS installation URL
   ```

### Method 3: Hook-Based Email (Custom)

For advanced customization, you can use WHMCS hooks.

**Create Hook File** (`includes/hooks/n8n_welcome_email.php`):

```php
<?php
add_hook('AfterModuleCreate', 1, function($vars) {
    if ($vars['producttype'] == 'server' && $vars['servertype'] == 'coolify') {
        // Send custom welcome email
        $emailData = [
            'messagename' => 'n8n_welcome_email',
            'id' => $vars['serviceid'],
            'customvars' => [
                'client_name' => $vars['clientsdetails']['firstname'] . ' ' . $vars['clientsdetails']['lastname'],
                'service_domain' => $vars['domain'],
                'service_id' => $vars['serviceid'],
                'service_product_name' => $vars['productname']
            ]
        ];
        
        sendMessage('General', $vars['serviceid'], $emailData);
    }
});
?>
```

## Email Content Customization

### Personalizing the Email

**Company Branding:**
```html
<!-- Update header colors -->
.header { background: linear-gradient(135deg, #YOUR_COLOR1 0%, #YOUR_COLOR2 100%); }

<!-- Update CTA button colors -->
.cta-button { background-color: #YOUR_BRAND_COLOR; }
```

**Custom Content:**
- Update company name and contact information
- Add your support URL and contact details
- Customize the feature descriptions
- Add your own resource links

### Template Variables

The email template supports these dynamic variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `{$client_name}` | Customer's full name | "John Smith" |
| `{$service_domain}` | n8n service URL | "n8n-john-123.yourdomain.com" |
| `{$service_id}` | WHMCS service ID | "12345" |
| `{$service_product_name}` | Product name | "n8n Workflow Automation" |
| `{$whmcs_url}` | WHMCS base URL | "https://billing.yourdomain.com/" |

## Testing the Welcome Email

### Test Email Sending

1. **Create Test Service**
   - Create a test n8n service in WHMCS
   - Monitor the Module Logs for email sending status

2. **Check Email Delivery**
   - Verify email arrives in customer's inbox
   - Test on different email clients (Gmail, Outlook, etc.)
   - Check mobile display

3. **Validate Links**
   - Ensure all links work correctly
   - Test n8n dashboard access
   - Verify resource links

### Debug Email Issues

**Check Module Logs:**
```
WHMCS Admin > System Logs > Module Log
Filter by: coolify
Look for: WelcomeEmail_Sent or WelcomeEmail_Error
```

**Common Issues:**
- **Email not sending**: Check WHMCS email configuration
- **Template not found**: Verify template file exists
- **Variables not working**: Check template variable syntax
- **Styling issues**: Test HTML in different email clients

## Email Analytics

### Tracking Email Performance

**WHMCS Email Logs:**
- Go to `System Logs > Email Message Log`
- Filter by template name or customer
- Monitor delivery status

**Custom Tracking:**
Add tracking pixels or UTM parameters to monitor:
- Email open rates
- Link click rates
- Customer engagement

### Improving Email Effectiveness

**A/B Testing:**
- Test different subject lines
- Try different CTA button colors
- Experiment with content layout

**Customer Feedback:**
- Survey customers about email usefulness
- Monitor support ticket reduction
- Track customer onboarding success

## Advanced Customization

### Multi-Language Support

Create language-specific templates:
```php
// In WelcomeEmailTemplate.php
function coolify_generateWelcomeEmail($params, $serviceInfo = null, $language = 'english') {
    switch($language) {
        case 'spanish':
            return coolify_generateWelcomeEmailSpanish($params, $serviceInfo);
        case 'french':
            return coolify_generateWelcomeEmailFrench($params, $serviceInfo);
        default:
            return coolify_generateWelcomeEmailEnglish($params, $serviceInfo);
    }
}
```

### Conditional Content

Show different content based on service type or customer:
```php
// Different content for different plans
if ($params['configoption1'] == 'premium') {
    $features .= '<li>Priority Support</li>';
    $features .= '<li>Advanced Integrations</li>';
}
```

### Integration with Marketing Tools

**Mailchimp Integration:**
```php
// Add customer to Mailchimp list after welcome email
function coolify_addToMailchimp($customerEmail, $customerName) {
    // Mailchimp API integration code
}
```

**Google Analytics:**
```html
<!-- Add UTM parameters to links -->
<a href="https://{$service_domain}?utm_source=welcome_email&utm_medium=email&utm_campaign=onboarding">
```

## Security Considerations

### Email Security
- **SPF/DKIM**: Configure proper email authentication
- **Content Filtering**: Avoid spam trigger words
- **Link Safety**: Use HTTPS for all links
- **Data Protection**: Don't include sensitive information

### Template Security
- **XSS Prevention**: Escape all user input
- **SQL Injection**: Use prepared statements
- **Access Control**: Restrict template file access

## Troubleshooting

### Common Issues

**Email Not Sending:**
1. Check WHMCS email configuration
2. Verify SMTP settings
3. Check server mail logs
4. Test with different email providers

**Template Not Loading:**
1. Verify file permissions
2. Check file path
3. Ensure proper PHP syntax
4. Test template variables

**Styling Issues:**
1. Test in multiple email clients
2. Use inline CSS for better compatibility
3. Avoid complex CSS features
4. Test on mobile devices

### Support Resources

- **WHMCS Documentation**: Email template guides
- **Module Logs**: Detailed error information
- **Community Forums**: User discussions and solutions
- **Professional Support**: Available for custom implementations

## Conclusion

The welcome email system provides a professional onboarding experience for n8n customers. With automatic sending, comprehensive content, and customization options, it helps reduce support requests and improves customer satisfaction.

For additional customization or support, refer to the module documentation or contact the development team.
