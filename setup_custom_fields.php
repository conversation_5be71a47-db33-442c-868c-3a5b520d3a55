<?php
/**
 * Setup Custom Fields for Coolify Plugin
 * Run this script to create required custom fields for existing Coolify products
 */

// Check if we're in WHMCS environment
if (!file_exists('configuration.php')) {
    echo "❌ This script should be run from your WHMCS root directory\n";
    echo "   Please copy this script to your WHMCS root and run it there.\n\n";
    exit(1);
}

// Load WHMCS
require_once 'init.php';

use Illuminate\Database\Capsule\Manager as Capsule;

echo "=== Setting Up Coolify Custom Fields ===\n\n";

try {
    $pdo = Capsule::connection()->getPdo();
    
    // Find all products that use Coolify server module
    $stmt = $pdo->prepare("SELECT p.id, p.name, s.name as server_name 
                          FROM tblproducts p 
                          LEFT JOIN tblservers s ON p.server = s.id 
                          WHERE p.servertype = 'coolify'");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "❌ No products found using Coolify server module\n";
        echo "   Please create a product with server type 'Coolify n8n' first\n\n";
        exit(1);
    }
    
    echo "Found " . count($products) . " Coolify product(s):\n";
    foreach ($products as $product) {
        echo "  - " . $product['name'] . " (ID: " . $product['id'] . ")\n";
    }
    echo "\n";
    
    $fieldsCreated = 0;
    $fieldsExisted = 0;
    
    foreach ($products as $product) {
        $productId = $product['id'];
        echo "Processing product: " . $product['name'] . "\n";
        
        // Define the custom fields we need
        $customFields = [
            'coolify_service_uuid' => 'Coolify Service UUID',
            'coolify_deployment_status' => 'Deployment Status',
            'coolify_last_deployment' => 'Last Deployment Date'
        ];
        
        foreach ($customFields as $fieldName => $description) {
            // Check if custom field already exists
            $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = ?");
            $stmt->execute([$productId, $fieldName]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                echo "  ✅ $fieldName already exists\n";
                $fieldsExisted++;
            } else {
                // Create the custom field
                $stmt = $pdo->prepare("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, fieldoptions, regexpr, adminonly, required, showorder, showinvoice) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute(['product', $productId, $fieldName, 'text', $description, '', '', '1', '0', '0', '0']);
                
                echo "  ✅ Created $fieldName\n";
                $fieldsCreated++;
            }
        }
        echo "\n";
    }
    
    echo "=== SUMMARY ===\n";
    echo "✅ Custom fields created: $fieldsCreated\n";
    echo "ℹ️  Custom fields already existed: $fieldsExisted\n";
    echo "🎉 Setup complete!\n\n";
    
    echo "Next steps:\n";
    echo "1. Try creating a service order again\n";
    echo "2. The custom field error should be resolved\n";
    echo "3. You can delete this script after successful testing\n\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   Please check your WHMCS database connection and try again.\n\n";
}
