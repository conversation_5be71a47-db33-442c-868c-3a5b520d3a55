# 🌐 Status Address Configuration Guide

## Overview

The Coolify module now uses the **Status Address** field in WHMCS server configuration for the Coolify API base URL. This provides a clean, dedicated field for API connectivity.

## 🎯 Why Status Address Field?

### Benefits
- ✅ **Dedicated API Field** - Specifically designed for service URLs
- ✅ **Clean Separation** - Hostname for display, Status Address for API
- ✅ **WHMCS Standard** - Uses built-in WHMCS functionality
- ✅ **No Validation Issues** - Accepts any URL format
- ✅ **Easy Management** - Clear purpose and usage

### Previous vs New Approach
```
❌ Before: IP Address field for API URL
✅ Now: Status Address field for API URL
```

## 📋 Configuration Format

### Status Address Field Format
```
http://************:8000
```

**Important Notes:**
- ✅ Include the protocol (`http://` or `https://`)
- ✅ Include the port number (usually `:8000`)
- ✅ Use the actual IP address or domain
- ✅ No trailing slash needed

## 🛠️ Server Configuration Examples

### Example 1: IP Address with Port
```
Server Name: Coolify Production
Hostname: coolify.yourdomain.com
Status Address: http://************:8000
Access Hash: your-api-token-here
```

### Example 2: Domain with Custom Port
```
Server Name: Coolify Staging
Hostname: coolify-staging.yourdomain.com
Status Address: https://coolify-staging.yourdomain.com:8000
Access Hash: your-api-token-here
```

### Example 3: Local Development
```
Server Name: Coolify Local
Hostname: localhost
Status Address: http://localhost:8000
Access Hash: your-local-api-token
```

### Example 4: Cloud Instance
```
Server Name: Coolify Cloud
Hostname: coolify.example.com
Status Address: https://coolify.example.com
Access Hash: your-cloud-api-token
```

## 🔄 How the Module Uses Status Address

### API URL Construction
The module automatically appends `/api/v1` to the status address:

```php
Status Address: http://************:8000
API URL: http://************:8000/api/v1
```

### Fallback Order
1. **Primary**: Status Address field
2. **Fallback**: IP Address field  
3. **Final Fallback**: Hostname field

### Code Implementation
```php
// Primary: Use Status Address field for Coolify API base URL
if (!empty($server['statusaddress'])) {
    $config['coolify_url'] = $server['statusaddress'];
}

// Fallback: Use IP Address field if status address is empty
if (empty($config['coolify_url']) && !empty($server['ipaddress'])) {
    $config['coolify_url'] = $server['ipaddress'];
}

// Final fallback: Use hostname if both above are empty
if (empty($config['coolify_url']) && !empty($server['hostname'])) {
    $config['coolify_url'] = $server['hostname'];
}
```

## 📝 Step-by-Step Setup

### Step 1: Access WHMCS Server Configuration
1. Go to **WHMCS Admin Area**
2. Navigate to **Setup > Products/Services > Servers**
3. Click **"Add New Server"** or edit existing server

### Step 2: Fill Basic Information
```
Server Name: Your Coolify Instance Name
Hostname: your-coolify-domain.com
Type: Other
Active: Yes
```

### Step 3: Configure Status Address
```
Status Address: http://************:8000
```

### Step 4: Add API Authentication
```
Access Hash: your-coolify-api-bearer-token
```

### Step 5: Configure Coolify Settings (Nameserver Fields)
```
Nameserver 1: project-uuid-here
Nameserver 2: server-uuid-here
Nameserver 3: destination-uuid-here
Nameserver 4: production
Nameserver 5: customers.yourdomain.com
```

### Step 6: Save and Test
1. Click **"Save Changes"**
2. Test by creating a service
3. Check module logs for connectivity

## 🌍 Multi-Instance Examples

### Production Environment
```
Server Name: Coolify Production
Hostname: coolify-prod.yourdomain.com
Status Address: https://coolify-prod.yourdomain.com:8000
Access Hash: prod-api-token
```

### Staging Environment
```
Server Name: Coolify Staging
Hostname: coolify-staging.yourdomain.com
Status Address: http://*************:8000
Access Hash: staging-api-token
```

### Development Environment
```
Server Name: Coolify Development
Hostname: coolify-dev.yourdomain.com
Status Address: http://localhost:8000
Access Hash: dev-api-token
```

## 🔍 Common Status Address Formats

### Standard Coolify Installation
```
http://your-server-ip:8000
https://your-domain.com:8000
```

### Custom Port Configuration
```
http://************:9000
https://coolify.yourdomain.com:9000
```

### SSL/TLS Enabled
```
https://************:8000
https://coolify.yourdomain.com
```

### Behind Reverse Proxy
```
https://coolify.yourdomain.com
https://api.coolify.yourdomain.com
```

## ⚠️ Important Notes

### Protocol Requirements
- **HTTP**: Use for local development or internal networks
- **HTTPS**: Use for production environments
- **Port**: Usually `:8000` for Coolify, but can be customized

### Security Considerations
- ✅ Use HTTPS in production
- ✅ Ensure API token has proper permissions
- ✅ Restrict network access to Coolify instance
- ✅ Use strong API tokens

### Network Requirements
- ✅ WHMCS server must reach Coolify instance
- ✅ Firewall rules must allow API access
- ✅ DNS resolution must work (if using domains)
- ✅ SSL certificates must be valid (if using HTTPS)

## 🧪 Testing Your Configuration

### Test API Connectivity
```bash
# Test basic connectivity
curl -H "Authorization: Bearer YOUR_TOKEN" http://************:8000/api/v1/projects

# Test with your actual configuration
curl -H "Authorization: Bearer YOUR_TOKEN" YOUR_STATUS_ADDRESS/api/v1/projects
```

### Test from WHMCS
1. Create a test n8n service
2. Check WHMCS Module Logs
3. Look for successful API calls
4. Verify service creation in Coolify

## 🔧 Troubleshooting

### "Connection refused"
- **Check**: Status Address is correct
- **Check**: Port is accessible
- **Check**: Coolify is running

### "Unauthorized"
- **Check**: Access Hash (API token) is correct
- **Check**: Token has proper permissions
- **Check**: Token is not expired

### "Invalid URL"
- **Check**: Status Address includes protocol
- **Check**: Port number is correct
- **Check**: No typos in URL

### "Timeout"
- **Check**: Network connectivity
- **Check**: Firewall rules
- **Check**: DNS resolution

## 📊 Configuration Validation

### Valid Status Address Examples
```
✅ http://************:8000
✅ https://coolify.yourdomain.com:8000
✅ https://coolify.yourdomain.com
✅ http://localhost:8000
```

### Invalid Status Address Examples
```
❌ ************:8000          (missing protocol)
❌ http://************        (missing port, if required)
❌ coolify.yourdomain.com     (missing protocol)
❌ http://************:8000/  (trailing slash not needed)
```

## 🎯 Best Practices

### For Production
- ✅ Use HTTPS with valid SSL certificates
- ✅ Use domain names instead of IP addresses
- ✅ Implement proper firewall rules
- ✅ Monitor API connectivity

### For Development
- ✅ Use HTTP for local testing
- ✅ Use localhost or local IP addresses
- ✅ Test with different port configurations
- ✅ Validate API responses

### For Multiple Environments
- ✅ Use descriptive server names
- ✅ Separate tokens for each environment
- ✅ Document each configuration
- ✅ Test connectivity regularly

## 🚀 Migration from Previous Configuration

### If You Were Using IP Address Field
1. **Copy** the URL from IP Address field
2. **Paste** into Status Address field
3. **Clear** IP Address field (optional)
4. **Test** the configuration

### If You Were Using Hostname Field
1. **Add protocol and port** to hostname URL
2. **Put complete URL** in Status Address field
3. **Keep hostname** as display name only
4. **Test** the configuration

The Status Address field provides a clean, dedicated way to configure your Coolify API connectivity! 🎉
